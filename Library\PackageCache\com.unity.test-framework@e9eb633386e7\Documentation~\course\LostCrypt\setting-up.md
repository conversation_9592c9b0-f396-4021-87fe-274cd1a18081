# 1\. Setting up LostCrypt

## Learning Objectives

In this exercise you'll set up a simple Unity 2D project and import a sample project (LostCrypt).

## Prerequisites

1.  **Unity 2020.3 LTS** - recommended version of Unity for this training session
2.  **C# IDE** (for example [Rider](https://www.jetbrains.com/rider/download/) or [Visual Studio](https://visualstudio.microsoft.com)) - not necessary but highly recommended. This way you can use features like a debugger and reliable syntax autocompletion.

## Exercise

1. Open **Unity Hub** and click _New Project_. Select a blank 2D (or Core2D) project.
2. Enter a **Project Name** and click **Create**.
3. Visit the [LostCrypt](https://assetstore.unity.com/packages/essentials/tutorial-projects/lost-crypt-2d-sample-project-158673) asset page. Click _Add to my Assets_ -> _Open in Unity Editor_.
4. **Package Manager** window opens automatically. Find **Lost Crypt - 2D Sample Project**. Press _Download_ and then _Import_.
5. **Import Unity Package** window opens. Click _Import_ to add all additional packages and assets to your newly created project.
6. Restart Unity if needed.

Now confirm that LostCrypt works correctly.

1.  From the **Project** tab open `Scenes/Main`.
2.  Enter Play Mode by clicking Play button.
3.  You should be able to move your character around.

## Further reading and Resources

You can read more about LostCrypt in [our blog post](https://www.google.com/url?q=https://blog.unity.com/technology/download-our-new-2d-sample-project-lost-crypt&source=gmail-html&ust=1653726008832000&usg=AOvVaw2RORHgX1nn7hE7KZW3e_lA).

## Hints (what can go wrong)

*   There might be some dependency problems - please make sure LostCrypt is downloaded for the suggested Unity LTS version.
*   Make sure you have the newest project packages in your Package Manager.
