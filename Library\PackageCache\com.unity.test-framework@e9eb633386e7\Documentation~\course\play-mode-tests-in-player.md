# 8\. PlayMode tests in a Player

## Learning objectives

This section will teach you how to run Play Mode tests in a Standalone player on your machine.

## Intro and motivation

A huge area in our quality assurance using automated testing is the ability to test on different types of platforms. This is both useful for us and our customers, allowing them to verify their application on everything from phones to consoles.  
  
The simplest setup to run on a platform is to run as standalone on your own computer. If you have the Unity standalone platform support for your OS installed, then you can run your Play Mode tests by clicking the **Run all in player** button. For more detailed instructions, see [Run play mode tests in a standalone player](../workflow-run-playmode-test-standalone.md).

## Exercise

Import the [sample](./welcome.md#import-samples) `8_PlayModeTests_InPlayer`.  
  
This project contains the solution to the previous exercise, which is just a simple Play Mode test.  
  
Execute the test in your standalone player by clicking the **Run all in player** button in the Play Mode tab.
