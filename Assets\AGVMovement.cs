using UnityEngine;
using UnityEngine.InputSystem; // Add this

public class AGVMovement : MonoBehaviour
{
    public Rigidbody agv;
    public float forwardForce = 1000f;
    public float rotationForce = 100f;

    // Reference to the action (set up in the Inspector or via code)
    public InputAction moveForward;
    public InputAction moveBackward;
    public InputAction moveLeft;
    public InputAction moveRight;

    private void OnEnable()
    {
        moveForward.Enable();
        moveBackward.Enable();
        moveLeft.Enable();
        moveRight.Enable();
    }

    private void OnDisable()
    {
        moveForward.Disable();
        moveBackward.Disable();
        moveLeft.Disable();
        moveRight.Disable();
    }

    // Update is called once per frame
    void FixedUpdate()
    {
        if (moveForward.ReadValue<float>() > 0)
        {
            // add a forward force to the AGV
            agv.AddForce(0, 0, forwardForce * Time.deltaTime);
        }
        if (moveBackward.ReadValue<float>() > 0)
        {
            // add a backward force to the AGV
            agv.AddForce(0, 0, -forwardForce * Time.deltaTime);
        }
        if (moveLeft.ReadValue<float>() > 0)
        {
            // add a left force to the AGV
            agv.AddForce(-forwardForce * Time.deltaTime, 0, 0);
        }
        if (moveRight.ReadValue<float>() > 0)
        {
            // add a right force to the AGV
            agv.AddForce(forwardForce * Time.deltaTime, 0, 0);
        }

    }
}
