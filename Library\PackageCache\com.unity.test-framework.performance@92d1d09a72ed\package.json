{"name": "com.unity.test-framework.performance", "displayName": "Performance testing API", "version": "3.1.0", "unity": "2020.3", "description": "Package that extends Unity Test Framework package. Adds performance testing capabilities and collects configuration metadata.", "keywords": ["performance", "test"], "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "relatedPackages": {"com.unity.test-framework.performance.tests": "3.1.0"}, "_upm": {"changelog": "### Added\n- Added an optional command-line argument \"perfTestResults\" to control the target location of performance test run results file.\n### Fixed\n- Warmup cycles no longer record GC measurements.\n- Setup and Cleanup cycles no longer contribute to GC measurements."}, "upmCi": {"footprint": "6dc909c752688d1bbff131dcf24d85b0388ebc8f"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.1/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git", "type": "git", "revision": "6126c0ee357019ed762100ffeca520029274e869"}, "_fingerprint": "92d1d09a72ed696fa23fd76c675b29d211664b50"}